import { Button } from '@/components/ui/button';
import { Combobox } from '@/components/ui/combobox';
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { SelectField } from '@/components/ui/select';
import { useApi } from '@/contexts/api';
import { useAuth } from '@/contexts/auth';
import { zodResolver } from '@hookform/resolvers/zod';
import { useQuery } from '@tanstack/react-query';
import { ChevronDown } from 'lucide-react';
import { Controller, useForm } from 'react-hook-form';
import { z } from 'zod';

const profileValidation = z.object({
  profile: z.string().min(1, 'Campo obrigatório'),
  company: z.object(
    {
      label: z.string(),
      value: z.string(),
    },
    { message: 'Campo obrigatório' },
  ),
});

type ProfileValidationType = z.infer<typeof profileValidation>;

export function ProfileChange() {
  const { user } = useAuth();

  const {
    clients: { loadClientCompanies },
  } = useApi();

  const {
    watch,
    control,
    handleSubmit,
    formState: { errors },
  } = useForm<ProfileValidationType>({
    resolver: zodResolver(profileValidation),
  });

  const { data: allCompaniesData } = useQuery({
    queryKey: ['allCompanies'],
    queryFn: () => loadClientCompanies(),
    refetchOnWindowFocus: false,
  });

  const handleSubmitForm = (data: ProfileValidationType) => {
    console.log(data);
  };

  return (
    <Dialog>
      <DialogTrigger asChild>
        <Button
          variant="outline"
          className="rounded-lg"
        >
          {user.currentRole.name}{' '}
          {user.currentRole.requiresCompany && ` - ${user.companyName ?? ''}`}
          <ChevronDown />
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[480px] p-8">
        <DialogHeader className="flex flex-col items-center mb-8">
          <DialogTitle>
            <span className="text-2xl">Trocar perfil/empresa</span>
          </DialogTitle>
        </DialogHeader>
        <form onSubmit={handleSubmit(handleSubmitForm)}>
          <div className="flex flex-col gap-10">
            <Controller
              name="profile"
              control={control}
              render={({ field }) => (
                <SelectField
                  label="Perfil"
                  placeholder="Selecione um perfil"
                  options={user.roles.map((role) => ({
                    label: role.description,
                    value: role.id,
                    requiresCompany: role?.requiresCompany,
                  }))}
                  onValueChange={field.onChange}
                  errorMessage={errors.profile?.message}
                />
              )}
            />
            {!!watch('profile') && (
              <Controller
                name="company"
                control={control}
                render={({ field }) => (
                  <Combobox
                    label="Empresa"
                    placeholder="Selecione uma empresa"
                    value={field.value}
                    onChange={field.onChange}
                    errorMessage={errors.company?.message}
                    options={
                      allCompaniesData?.result.map((company) => ({
                        value: company.id,
                        label: company.name,
                      })) ?? []
                    }
                  />
                )}
              />
            )}

            <div className="flex w-full justify-between gap-8">
              <DialogClose asChild>
                <Button
                  variant="outline"
                  className="flex-1 h-10"
                  type="button"
                >
                  Cancelar
                </Button>
              </DialogClose>
              <Button
                type="submit"
                className="flex-1 h-10"
              >
                Salvar
              </Button>
            </div>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}
